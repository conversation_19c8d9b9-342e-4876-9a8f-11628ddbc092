package com.landray.oppo.missive.core.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.landray.common.core.entity.AbstractEntity;
import com.landray.framework.meta.MetaConstant;
import com.landray.framework.meta.annotation.MetaEntity;
import com.landray.framework.meta.annotation.MetaProperty;
import com.landray.oppo.missive.support.common.entity.AbstractCreateInfoEntity;
import com.landray.sys.auth.filter.annotation.AuthFieldFilter;
import com.landray.sys.auth.filter.annotation.AuthFieldFilters;
import com.landray.sys.category.entity.CategoryEntity;
import com.landray.sys.org.entity.SysOrgElementSummary;
import com.landray.sys.xform.support.annotation.XFormMetaProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * 公文模板分类
 *
 * <AUTHOR>
 * @date 2021-04-19
 */
@Getter
@Setter
@Entity
@Table
@MetaEntity(displayProperty = "fdName", messageKey = "oppo-missive:table.oppoMissiveCategory")
@AuthFieldFilters(filters = {
		@AuthFieldFilter(fieldName = "fdCreator.fdId", filterType = "creator"),
})
public class OppoMissiveCategory extends AbstractEntity implements CategoryEntity<OppoMissiveCategory, OppoMissiveCategoryPerm>, AbstractCreateInfoEntity {

	/**
	 * 分类图标
	 */
	private String fdIcon;

	/**
	 * 字号
	 */
	@ApiModelProperty("字号")
	@MetaProperty(messageKey = "oppo-missive:oppoMissiveCategory.fdWordSize")
	private String fdWordSize;

	@ApiModelProperty("公司编码")
	@MetaProperty(messageKey = "oppo-missive:oppoMissiveCategory.fdMenuCode")

	private String fdMenuCode;
	/**
	 * 公文类型，1：公文类别 2.公文级别 3.发文机构 4.组织
	 */
	@ApiModelProperty("公文类型，1：公文类别 2.公文级别 3.发文机构 4.组织")
	@MetaProperty(messageKey = "oppo-missive:oppoMissiveCategory.fdType")
	private Integer fdType;

	/**
	 * 权限相关，可阅读者，可编辑者
	 */
	@OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, mappedBy = "fdMainDoc")
	@JsonIgnore
	@JSONField(serialize = false)
	private List<OppoMissiveCategoryPerm> fdPermissions;

	@MetaProperty(
			showType = MetaConstant.ShowType.ALWAYS,
			messageKey = "property.fdTreeLevel"
	)
	@Min(1L)
	private Integer fdTreeLevel;

	@ApiModelProperty("审批环节审批人")
	@XFormMetaProperty(order = 11, fixedLayout = true)
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "oppo_missive_category_approval",
			joinColumns = {@JoinColumn(name = "fd_doc_id", referencedColumnName = "fdId")},
			inverseJoinColumns = {@JoinColumn(name = "fd_org_id", referencedColumnName = "fdId")})
	@MetaProperty(messageKey = "oppo-missive:oppoMissiveCategory.fdApproval")
	private List<SysOrgElementSummary> fdApproval;

	@ApiModelProperty("会签环节审批人")
	@XFormMetaProperty(order = 11, fixedLayout = true)
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "oppo_missive_category_countersign",
			joinColumns = {@JoinColumn(name = "fd_doc_id", referencedColumnName = "fdId")},
			inverseJoinColumns = {@JoinColumn(name = "fd_org_id", referencedColumnName = "fdId")})
	@MetaProperty(messageKey = "oppo-missive:oppoMissiveCategory.fdCountersign")
	private List<SysOrgElementSummary> fdCountersign;

	@ApiModelProperty("批准环节审批人")
	@XFormMetaProperty(order = 11, fixedLayout = true)
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "oppo_missive_category_ratify",
			joinColumns = {@JoinColumn(name = "fd_doc_id", referencedColumnName = "fdId")},
			inverseJoinColumns = {@JoinColumn(name = "fd_org_id", referencedColumnName = "fdId")})
	@MetaProperty(messageKey = "oppo-missive:oppoMissiveCategory.fdRatify")
	private List<SysOrgElementSummary> fdRatify;

	@ApiModelProperty("签发人")
	@XFormMetaProperty(order = 11, fixedLayout = true)
	//@AddressExtendMetaProperty(orgTypeArr = {"8"})
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "oppo_missive_category_issuer",
			joinColumns = {@JoinColumn(name = "fd_doc_id", referencedColumnName = "fdId")},
			inverseJoinColumns = {@JoinColumn(name = "fd_org_id", referencedColumnName = "fdId")})
	@MetaProperty(messageKey = "oppo-missive:oppoMissiveCategory.fdIssuer")
	private List<SysOrgElementSummary> fdIssuer;

}
